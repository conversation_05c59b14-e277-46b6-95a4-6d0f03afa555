import { Component } from '@angular/core';
import { IonApp, IonMenu, IonRouterOutlet } from '@ionic/angular/standalone';
import { ToastModule } from 'primeng/toast';
import { MenuModule } from 'primeng/menu';
@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  imports: [
    IonApp, 
    IonMenu, 
    IonRouterOutlet,
    ToastModule,
    MenuModule
  ],
})
export class AppComponent {
  items: any[] = [
    { label: 'Billing', icon: 'fi fi-rr-shopping-cart', routerLink: '/home' },
    { label: 'Invoices', icon: 'fi fi-rr-file-invoice-dollar', routerLink: '/invoices' },
    { label: 'Products', icon: 'fi fi-rr-box', routerLink: '/products' },
    { label: 'Settings', icon: 'fi fi-rr-user', routerLink: '/settings' },
    { label: 'Logout', icon: 'fi fi-rr-sign-out-alt', routerLink: '/logout' },
  ];
  constructor() {
  }
}
