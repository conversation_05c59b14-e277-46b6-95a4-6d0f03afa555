import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IftaLabelModule } from 'primeng/iftalabel';
import { InputTextModule } from 'primeng/inputtext';
import { NoDataComponent } from '../no-data/no-data';
import { ProductComponent } from '../product/product';
import { TableModule } from 'primeng/table';
import { InputNumberModule } from 'primeng/inputnumber';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { CommonService } from '../../services/common';
import { TypeSenseService } from '../../services/typesense';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@Component({
  selector: 'app-billing',
  standalone: true,
  templateUrl: './billing.html',
  imports: [
    CommonModule,
    FormsModule,
    IftaLabelModule,
    InputTextModule,
    NoDataComponent,
    ProductComponent,
    TableModule,
    InputNumberModule,
    DividerModule,
    ButtonModule,
    AutoCompleteModule,
    ProgressSpinnerModule,
  ],
})
export class BillingComponent implements OnChanges {
  @Input() cartItems: any[] = [];
  @Input() quantityEdit = false;
  @Input() showActions = false;
  @Output() searchEvent = new EventEmitter<{
    searchText: string;
    event: any;
    keyCode: number;
  }>();
  searchText: string = '';
  selectedProduct: any = null;
  products: any[] = [];
  filteredProducts: any[] = [];
  selectedIndex: number = 0;


  // Checkout properties
  isProcessingCheckout = false;

  @ViewChild('searchInput') searchInput: any;
  constructor(
    private commonService: CommonService,
    private typesenseService: TypeSenseService,
    private cdr: ChangeDetectorRef,
  ) {}
  ngOnChanges(_changes: SimpleChanges): void {
    console.log(this.cartItems);
  }

  addToCart(product: any) {
    console.log('=== ADDING PRODUCT TO CART ===');
    console.log('Product being added:', product);
    console.log('Product keys:', Object.keys(product));
    console.log('Product name:', product.name);
    console.log('Product image:', product.image);
    console.log('Product thumbnail_url:', product.thumbnail_url);
    console.log('Product sku:', product.sku);

    const cartItem = this.cartItems?.find(
      (item: any) => item.id === product.id,
    );
    if (cartItem) {
      cartItem.quantity += 1;
      console.log('Updated existing cart item quantity:', cartItem.quantity);
    } else {
      const newCartItem = { ...product, quantity: 1 };
      console.log('New cart item being added:', newCartItem);
      this.cartItems?.push(newCartItem);
      this.cdr.detectChanges();
    }

    console.log('Cart items after addition:', this.cartItems);

    // Clear the search and selection after adding to cart
    this.products = [];
    this.selectedProduct = null;
    this.filteredProducts = [];
  }

  onSearchChange(event: any) {
    const query = event.query;
    if (query && query.length > 2) {
      this.getsearchProducts(query);
    } else {
      this.filteredProducts = [];
    }
  }
  getsearchProducts(query: string) {
    this.typesenseService.searchProducts(query).then((result) => {
      this.filteredProducts = result.products || [];
      console.log('Search results for query:', query);
      console.log('Filtered products:', this.filteredProducts);
      if (this.filteredProducts.length > 0) {
        console.log('First product structure:', this.filteredProducts[0]);
        console.log('First product keys:', Object.keys(this.filteredProducts[0]));
      }
    });
  }
  removeFromCart(product: any) {
    const index = this.cartItems.findIndex((item) => item.id === product.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
    }
  }
  updateQuantity(event: any, product: any) {
    const newQuantity = event.value;

    if (newQuantity < 1) {
      // Remove item from cart if quantity is less than 1
      this.removeFromCart(product);
    } else {
      product.quantity = newQuantity;
    }
  }
  getSubTotal() {
    return this.cartItems.reduce(
      (total, item) => total + item.price * item.quantity,
      0,
    );
  }
  getDiscount() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) * 0.1
    );
  }
  getGrandTotal() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) - this.getDiscount()
    );
  }
  onSearch(event: any) {
    this.searchEvent.emit({
      searchText: event.target.value,
      event,
      keyCode: event.keyCode || 0,
    });
  }
  onClearSearch() {
    this.searchText = '';
  }
  clearCart() {
    this.cartItems = [];
  }

  async checkout() {
    if (this.cartItems.length === 0) {
      this.commonService.toast({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Cart is empty. Add items to checkout.',
      });
      return;
    }

    // Set auth token for API call
    localStorage.setItem('auth_token', 'test-token');

    this.isProcessingCheckout = true;

    try {
      const totalAmount = this.getGrandTotal();
      // Create order payload 
      const orderData = {
        customer_id: 'CUST-001',
        customer_name: 'Test User',
        facility_id: 'FAC-001',
        facility_name: 'Test Facility',
        total_amount: totalAmount,
        items: this.cartItems.map((item) => ({
          sku: item.sku,
          unit_price: item.price,
          quantity: item.quantity,
          sale_price: item.price,
        }))
      };
      // create order api 
      this.commonService.post('create_order', orderData).subscribe({
        next: (response) => {
          console.log('Order created successfully:', response);
          this.commonService.toast({
            severity: 'success',
            summary: 'Success',           
            detail: `Order created successfully! Total: ₹${totalAmount.toFixed(2)}`
          });
          this.clearCart();
          this.isProcessingCheckout = false;
        },
        error: (error) => {
          console.error('Order creation error:', error);
          let errorMessage = 'Order creation failed. Please try again.';

          if (error.status === 401) {
            errorMessage = 'Authentication failed. Please check your token.';
          } else if (error.status === 400) {
            errorMessage = 'Invalid order data. Please check the items.';
          }

          this.commonService.toast({
            severity: 'error',
            summary: 'Error',
            detail: errorMessage
          });
          this.isProcessingCheckout = false;
        }
      });
    } catch (error) {
      console.error('Error enriching cart items:', error);
      this.commonService.toast({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to fetch product details. Please try again.'
      });
      this.isProcessingCheckout = false;
    }
  }
}