import { Component } from "@angular/core";
import { IonContent, IonHeader, IonToolbar, IonButtons, IonTitle, IonMenuButton } from "@ionic/angular/standalone";

@Component({
    selector: 'app-header',
    standalone: true,
    template: `
     <ion-header [translucent]="true">
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-menu-button autoHide="false"></ion-menu-button>
          </ion-buttons>
          <ion-title>POS</ion-title>
        </ion-toolbar>
    </ion-header>
    `,
    imports: [IonTitle, IonButtons, IonToolbar, IonHeader, IonMenuButton ]
})
export class HeaderComponent {
}