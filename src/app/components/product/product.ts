import { Component, input, Input } from "@angular/core";
import { ImageModule } from "primeng/image";
import { FormsModule } from "@angular/forms";
import { CommonModule } from "@angular/common";
@Component({
    selector: 'app-product',
    standalone: true,
    imports: [ImageModule, FormsModule, CommonModule],
    templateUrl: './product.html',
})
export class ProductComponent {
    @Input() product: any = {};
    @Input() imageWidth: string = '50px';
    @Input() imageHeight: string = '50px';
    @Input() showPrice: boolean = true;
    @Input() bgColor: string = 'bg-white';
    @Input() showSku: boolean = false;
    onImageError(event: any) {
        event.target.src = 'https://www.shorekids.co.nz/wp-content/uploads/2014/08/image-placeholder.jpg';
    }
}
