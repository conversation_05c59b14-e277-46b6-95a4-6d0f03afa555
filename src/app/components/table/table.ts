import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, SimpleChanges, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { IftaLabelModule } from 'primeng/iftalabel';
@Component({
  selector: 'app-table',
  standalone: true,
  templateUrl: './table.html',
  imports: [
    CommonModule, FormsModule, TableModule, IconFieldModule, InputIconModule, InputTextModule, IftaLabelModule
  ]
})
export class TableComponent {
  @Input() selection = false;
  @Input() selectionMode: 'multiple' | 'single' | null | undefined = 'multiple';
  @Input() tableData: any[] = [];
  @Input() tableColumns: any[] = [];
  @Input() selectedRows: any[] = [];
  @Input() dataKey: string = '';
  @Input() globalFilterFields: string[] = [];
  @Output() onSelect = new EventEmitter<any>();
  ngOnChanges(changes: SimpleChanges): void {
    this.globalFilterFields = this.tableColumns?.map((col: any) => col?.field || '');
  }
  onRowSelect(event: any, table: any) {
    this.onSelect.emit({event, table, selectedRows: this.selectedRows});
  }
  onSelectAll(event: any, table: any) {
    if (event.checked) {
      this.selectedRows = [...this.tableData];
    } else {
      this.selectedRows = [];
    }
    this.onSelect.emit({event, table, selectedRows: this.selectedRows});
  }
}