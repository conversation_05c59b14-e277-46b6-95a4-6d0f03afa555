<ion-content [fullscreen]="true">
    <app-header></app-header>
    <div class="grid grid-cols-3 gap-4 p-4">
      <div class="col-span-2">
        <div class="w-full p-4">
          <h6>Categories</h6>
          <div class="flex flex-wrap items-center gap-4 mt-3">
            <div
              (click)="getProductsByCategory(category.name)"
              class="w-[100px] h-[140px] mb-2 flex items-center justify-center gap-2"
              *ngFor="let category of categories"
            >
              <app-category
                class="w-full"
                [category]="getCategoryWithTotalItems(category)"
              ></app-category>
            </div>
          </div>
        </div>
        <div class="w-full mt-3 p-4">
          <h6>Products</h6>
          <div
            class="max-h-[380px] scrollbar-thin overflow-y-auto border border-gray-200 rounded-lg p-3 mt-3"
            (scroll)="onProductsScroll($event)">

            <div class="grid grid-cols-4 gap-4">
              <div
                class="w-full flex items-center gap-2"
                *ngFor="let product of products; trackBy: trackByProductId"
                (click)="addToCart(product)"
              >
                <app-product class="w-full" [product]="product"></app-product>
              </div>
            </div>

            <!-- Loading Indicator -->
            <div class="w-full flex items-center justify-center py-4" *ngIf="isLoadingProducts">
              <p-progressSpinner
                [style]="{'width': '30px', 'height': '30px'}"
                strokeWidth="4"
                animationDuration="1s">
              </p-progressSpinner>
              <span class="ml-3 text-sm text-gray-600">Loading more products...</span>
            </div>

            <!-- No More Products Message -->
            <div class="w-full text-center py-4 text-gray-500 text-sm" *ngIf="!hasMoreProducts && products.length > 0 && !isLoadingProducts">
              No more products to load
            </div>

            <!-- No Products Message -->
            <div class="w-full text-center py-8" *ngIf="products.length === 0 && !isLoadingProducts">
              <p class="text-gray-500">No products available for this category</p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-1 p-4">
        <app-billing
          [showActions]="true"
          [quantityEdit]="true"
          #billingComponent
          (searchEvent)="addItemToCart($event)"
        ></app-billing>
      </div>
    </div>
  </ion-content>