import { Component, On<PERSON><PERSON>roy } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IonContent } from "@ionic/angular/standalone";
import { HeaderComponent } from "../../components/header/header";
import { TableComponent } from "../../components/table/table";
import { BillingComponent } from "../../components/billing/billing";
import { DataService } from "src/app/services/data";
import { CommonService } from "src/app/services/common";
import { TypeSenseService } from "src/app/services/typesense";
import { Subscription } from "rxjs";
@Component({
    selector: 'app-invoices',
    standalone: true,
    templateUrl: './invoices.html',
    imports: [CommonModule, IonContent, HeaderComponent, TableComponent, BillingComponent]
})
export class InvoicesComponent implements OnDestroy {
    invoices: any[] = [];
    invoicesColumns: any[] = [];
    cartItems: any[] = [];
    isLoading = false;
    errorMessage = '';
    ordersData: any[] = [];
    lastRefreshTime: string = '';
    private orderCreatedSubscription: Subscription;

    constructor(
        private data: DataService,
        private commonService: CommonService,
        private typesenseService: TypeSenseService
    ) {
        this.invoicesColumns = [
            { field: 'order_id', header: 'Order ID' },
            { field: 'customer_name', header: 'Customer' },
            { field: 'created_at', header: 'Date' },
            { field: 'status', header: 'Status' },
            { field: 'total_amount', header: 'Total' },
        ];
        this.loadOrders();

        // Subscribe to order creation notifications
        this.orderCreatedSubscription = this.commonService.orderCreated$.subscribe((orderCreated) => {
            if (orderCreated) {
                console.log('New order created, force refreshing invoice list...');
                this.forceRefreshOrders();
                // Reset the notification
                this.commonService.resetOrderCreatedNotification();
            }
        });
    }

    ngOnDestroy(): void {
        if (this.orderCreatedSubscription) {
            this.orderCreatedSubscription.unsubscribe();
        }
    }

    // Force refresh orders - clear cache and reload
    forceRefreshOrders(): void {
        console.log('Force refreshing orders...');
        // Clear current data
        this.invoices = [];
        this.cartItems = [];
        this.ordersData = [];
        this.errorMessage = '';

        // Load fresh data
        this.loadOrders();
    }

    // Try alternative API endpoints if the main one is limited
    tryAlternativeOrdersAPI(): void {
        console.log('Trying alternative orders API...');
        this.isLoading = true;
        this.errorMessage = '';

        // Try different possible endpoints
        const alternativeEndpoints = [
            'orders',
            'get_orders',
            'all_orders',
            'orders/all'
        ];

        // Try each endpoint
        alternativeEndpoints.forEach((endpoint, index) => {
            setTimeout(() => {
                console.log(`Trying endpoint: ${endpoint}`);
                this.commonService.get(endpoint, { limit: 100, timestamp: new Date().getTime() }).subscribe({
                    next: (response) => {
                        console.log(`Response from ${endpoint}:`, response);
                    },
                    error: (error) => {
                        console.log(`Error from ${endpoint}:`, error);
                    }
                });
            }, index * 1000); // Stagger requests
        });
    }
    loadOrders() {
        this.isLoading = true;
        this.errorMessage = '';

        // Set auth token for API call (same as in checkout)
        localStorage.setItem('auth_token', 'test-token');

        // Add query parameters to get all orders and prevent caching
        const queryParams = {
            limit: 100, // Request more orders
            page: 1,
            timestamp: new Date().getTime() // Cache busting
        };

        console.log('Loading orders with params:', queryParams);

        this.commonService.get('get_all_orders', queryParams).subscribe({
            next: (response) => {
                console.log('=== FULL API RESPONSE ===');
                console.log('Orders fetched successfully:', response);
                console.log('Response type:', typeof response);
                console.log('Response keys:', Object.keys(response || {}));
                console.log('Full response JSON:', JSON.stringify(response, null, 2));

                // Handle different possible response structures
                let ordersArray: any[] = [];

                if (Array.isArray(response)) {
                    // If response is directly an array
                    ordersArray = response;
                    console.log('Response is direct array, length:', ordersArray.length);
                } else if (response && (response as any).orders) {
                    // If response has orders property
                    ordersArray = (response as any).orders;
                    console.log('Response has orders property, length:', ordersArray.length);
                } else if (response && Array.isArray((response as any).data)) {
                    // If response has data property with array
                    ordersArray = (response as any).data;
                    console.log('Response has data property, length:', ordersArray.length);
                } else {
                    console.log('Unknown response structure:', response);
                }

                console.log('=== ORDERS ARRAY FROM BACKEND ===');
                console.log('Orders array before mapping:', ordersArray);
                console.log('Total orders received:', ordersArray.length);

                if (ordersArray.length > 0) {
                    console.log('First order from backend:', ordersArray[0]);
                    console.log('First order items from backend:', ordersArray[0]?.items);
                    if (ordersArray[0]?.items && Array.isArray(ordersArray[0].items) && ordersArray[0].items.length > 0) {
                        console.log('First item from backend:', ordersArray[0].items[0]);
                        console.log('First item keys from backend:', Object.keys(ordersArray[0].items[0] || {}));
                    }
                } else {
                    console.log('No orders found in response');
                }

                this.invoices = ordersArray.map((order: any, index: number) => {
                    console.log(`Processing order ${index + 1}:`, order);

                    // Ensure items exist and is an array
                    const items = Array.isArray(order?.items) ? order.items : [];
                    console.log(`Order ${index + 1} items from backend:`, items);

                    return {
                        ...order,
                        // Ensure required fields have default values
                        order_id: order?.order_id || `ORDER-${index + 1}`,
                        customer_name: order?.customer_name || 'Unknown Customer',
                        created_at: order?.created_at || new Date().toISOString(),
                        status: order?.status || 'pending',
                        total_amount: order?.total_amount || 0,
                        items: items.map((item: any, itemIndex: number) => ({
                            ...item,
                            sku: item?.sku || `SKU-${itemIndex}`,
                            price: item?.sale_price || item?.price || 0,
                            quantity: item?.quantity || 1,
                            name: item?.name || 'Unknown Product'
                        }))
                    };
                });

                console.log('Final mapped orders:', this.invoices);
                console.log('Total orders after mapping:', this.invoices.length);

                // Set first order's items as default selection if available
                if (this.invoices.length > 0) {
                    this.ordersData = this.invoices[0];
                    this.cartItems = Array.isArray((this.ordersData as any)?.items) ? (this.ordersData as any).items : [];
                    console.log('Selected order data:', this.ordersData);
                    console.log('Cart items:', this.cartItems);
                    console.log('Cart items count:', this.cartItems.length);
                } else {
                    console.log('No orders available to select');
                    this.ordersData = [];
                    this.cartItems = [];
                }
                this.isLoading = false;
                this.lastRefreshTime = new Date().toLocaleTimeString();
            },
            error: (error) => {
                console.error('Error fetching orders:', error);
                this.errorMessage = 'Failed to load orders. Please try again.';
                this.isLoading = false;
                this.lastRefreshTime = new Date().toLocaleTimeString();
            }
        });
    }
    async onSelect(event: any){
        console.log('Order selection event:', event);

        //real API data
        this.ordersData = event?.event?.data || {};
        console.log('Selected order data:', this.ordersData);

        // Handle both array and object cases safely
        let rawItems: any[] = [];
        if (Array.isArray(this.ordersData)) {
            // If ordersData is an array, get items from first element
            rawItems = this.ordersData.length > 0 && this.ordersData[0]?.items ? this.ordersData[0].items : [];
        } else {
            // If ordersData is an object, get items directly
            rawItems = Array.isArray((this.ordersData as any)?.items) ? (this.ordersData as any).items : [];
        }

        console.log('Raw items from selected order:', rawItems);
        console.log('Raw items count:', rawItems.length);

        if (rawItems.length > 0) {

            // Enrich items with complete product details from TypeSense
            const enrichedItems = await Promise.all(
                rawItems.map(async (item: any) => {
                    console.log('Processing invoice item:', item);
                    console.log('Item SKU:', item.sku);

                    if (!item.sku) {
                        console.warn('Invoice item missing SKU, using item data directly:', item);
                        return {
                            ...item,
                            name: item.name || 'Unknown Product',
                            image: item.image || item.thumbnail_url || '',
                            thumbnail_url: item.thumbnail_url || item.image || ''
                        };
                    }

                    const productDetails = await this.typesenseService.getProductBySku(item.sku);

                    if (productDetails) {
                        console.log('Found product details for invoice item SKU:', item.sku, productDetails);
                        // Merge backend item data with complete product details from TypeSense
                        return {
                            ...item, // Keep backend data (quantity, prices, status, etc.)
                            // Override with complete product details
                            // id: item.id || item.sku,
                            name: productDetails.name || item.name || 'Unknown Product',
                            description: productDetails.description || '',
                            image: productDetails.image_url?.[0] || productDetails.thumbnail_url || '',
                            thumbnail_url: productDetails.thumbnail_url || productDetails.image_url?.[0] || '',
                            image_url: productDetails.image_url || [],
                            // Keep pricing from backend (in case it's different from TypeSense)
                            // price: item.sale_price || item.unit_price || item.price || productDetails.price,
                            created_at: productDetails.created_at || '',
                            updated_at: productDetails.updated_at || ''
                        };
                    } else {
                        console.warn(`Product with SKU ${item.sku} not found in TypeSense, using backend data`);
                        return {
                            ...item,
                            id: item.id || item.sku,
                            name: item.name || 'Unknown Product',
                            description: item.description || '',
                            image: item.image || item.thumbnail_url || '',
                            thumbnail_url: item.thumbnail_url || item.image || '',
                            price: item.sale_price || item.unit_price || item.price || 0,
                        };
                    }
                })
            );

            this.cartItems = enrichedItems;
            console.log('Final enriched cart items:', this.cartItems);
            console.log('Final cart items count:', this.cartItems.length);
        } else {
            console.log('No items found in selected order');
            this.cartItems = [];
        }
    }
}