import { Component } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IonContent } from "@ionic/angular/standalone";
import { HeaderComponent } from "../../components/header/header";
import { TableComponent } from "../../components/table/table";
import { BillingComponent } from "../../components/billing/billing";
import { DataService } from "src/app/services/data";
import { CommonService } from "src/app/services/common";
import { TypeSenseService } from "src/app/services/typesense";
@Component({
    selector: 'app-invoices',
    standalone: true,
    templateUrl: './invoices.html',
    imports: [CommonModule, IonContent, HeaderComponent, TableComponent, BillingComponent]
})
export class InvoicesComponent {
    invoices: any[] = [];
    invoicesColumns: any[] = [];
    cartItems: any[] = [];
    isLoading = false;
    errorMessage = '';
    ordersData: any[] = [];

    constructor(
        private data: DataService,
        private commonService: CommonService,
        private typesenseService: TypeSenseService
    ) {
        this.invoicesColumns = [
            { field: 'order_id', header: 'Order ID' },
            { field: 'customer_name', header: 'Customer' },
            { field: 'created_at', header: 'Date' },
            { field: 'status', header: 'Status' },
            { field: 'total_amount', header: 'Total' },
        ];
        this.loadOrders();
    }
    loadOrders() {
        this.isLoading = true;
        this.errorMessage = '';

        // Set auth token for API call (same as in checkout)
        localStorage.setItem('auth_token', 'test-token');

        this.commonService.get('get_all_orders').subscribe({
            next: (response) => {
                console.log('Orders fetched successfully:', response);
                console.log('Response type:', typeof response);
                console.log('Response keys:', Object.keys(response || {}));

                // Handle different possible response structures
                let ordersArray: any[] = [];

                if (Array.isArray(response)) {
                    // If response is directly an array
                    ordersArray = response;
                    console.log('Response is direct array, length:', ordersArray.length);
                } else if (response && (response as any).orders) {
                    // If response has orders property
                    ordersArray = (response as any).orders;
                    console.log('Response has orders property, length:', ordersArray.length);
                } else if (response && Array.isArray((response as any).data)) {
                    // If response has data property with array
                    ordersArray = (response as any).data;
                    console.log('Response has data property, length:', ordersArray.length);
                } else {
                    console.log('Unknown response structure:', response);
                }

                console.log('=== ORDERS ARRAY FROM BACKEND ===');
                console.log('Orders array before mapping:', ordersArray);
                if (ordersArray.length > 0) {
                    console.log('First order from backend:', ordersArray[0]);
                    console.log('First order items from backend:', ordersArray[0].items);
                    if (ordersArray[0].items && ordersArray[0].items.length > 0) {
                        console.log('First item from backend:', ordersArray[0].items[0]);
                        console.log('First item keys from backend:', Object.keys(ordersArray[0].items[0]));
                    }
                }

                this.invoices = ordersArray.map((order: any) => {
                    console.log('Processing order:', order);

                    // Ensure items exist and is an array
                    const items = Array.isArray(order.items) ? order.items : [];
                    console.log('Order items from backend:', items);

                    return {
                        ...order,
                        items: items.map((item: any) => ({
                            ...item,
                            sku : item.sku,
                            price: item.sale_price || item.price || 0,
                            quantity: item.quantity || 1
                        }))
                    };
                });

                console.log('Final mapped orders:', this.invoices);

                // Set first order's items as default selection if available
                if (this.invoices.length > 0) {
                    this.ordersData = this.invoices[0];
                    this.cartItems = (this.ordersData as any).items || [];
                    console.log('Selected order data:', this.ordersData);
                    console.log('Cart items:', this.cartItems);
                }
                this.isLoading = false;
            },
            error: (error) => {
                console.error('Error fetching orders:', error);
                this.errorMessage = 'Failed to load orders. Please try again.';
                this.isLoading = false;

               
                
            }
        });
    }
    async onSelect(event: any){
        //real API data 
        this.ordersData = event?.event?.data || [];
        const rawItems = Array.isArray(this.ordersData) ? this.ordersData[0]?.items || [] : (this.ordersData as any).items || [];

        if (rawItems.length > 0) {

            // Enrich items with complete product details from TypeSense
            const enrichedItems = await Promise.all(
                rawItems.map(async (item: any) => {
                    console.log('Processing invoice item:', item);
                    console.log('Item SKU:', item.sku);

                    if (!item.sku) {
                        console.warn('Invoice item missing SKU, using item data directly:', item);
                        return {
                            ...item,
                            name: item.name || 'Unknown Product',
                            image: item.image || item.thumbnail_url || '',
                            thumbnail_url: item.thumbnail_url || item.image || ''
                        };
                    }

                    const productDetails = await this.typesenseService.getProductBySku(item.sku);

                    if (productDetails) {
                        console.log('Found product details for invoice item SKU:', item.sku, productDetails);
                        // Merge backend item data with complete product details from TypeSense
                        return {
                            ...item, // Keep backend data (quantity, prices, status, etc.)
                            // Override with complete product details
                            // id: item.id || item.sku,
                            name: productDetails.name || item.name || 'Unknown Product',
                            description: productDetails.description || '',
                            image: productDetails.image_url?.[0] || productDetails.thumbnail_url || '',
                            thumbnail_url: productDetails.thumbnail_url || productDetails.image_url?.[0] || '',
                            image_url: productDetails.image_url || [],
                            // Keep pricing from backend (in case it's different from TypeSense)
                            // price: item.sale_price || item.unit_price || item.price || productDetails.price,
                            created_at: productDetails.created_at || '',
                            updated_at: productDetails.updated_at || ''
                        };
                    } else {
                        console.warn(`Product with SKU ${item.sku} not found in TypeSense, using backend data`);
                        return {
                            ...item,
                            id: item.id || item.sku,
                            name: item.name || 'Unknown Product',
                            description: item.description || '',
                            image: item.image || item.thumbnail_url || '',
                            thumbnail_url: item.thumbnail_url || item.image || '',
                            price: item.sale_price || item.unit_price || item.price || 0,
                        };
                    }
                })
            );

            this.cartItems = enrichedItems;
        } else {
            
        }    
    }
}