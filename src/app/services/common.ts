import { HttpClient, HttpErrorResponse, HttpHeaders } from "@angular/common/http";
import { Injectable, OnDestroy } from "@angular/core";
import { MessageService } from "primeng/api";
import { Observable, Subject, throwError, BehaviorSubject } from "rxjs";
import { catchError, finalize, takeUntil } from "rxjs/operators";
import { environment } from "src/environments/environment";

@Injectable({
    providedIn: 'root'
})
export class CommonService implements OnDestroy {
    baseUrl = environment.baseUrl;
    private destroy$ = new Subject<void>();
    private loading = false;

    // Order refresh notification system
    private orderCreatedSubject = new BehaviorSubject<boolean>(false);
    public orderCreated$ = this.orderCreatedSubject.asObservable();
    
    constructor(
        private http: HttpClient,
        private message: MessageService
    ) { }
    
    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    toast(data: { severity: string, summary: string, detail: string }): void {
        this.message.add(data);
    }
    
    isLoading(): boolean {
        return this.loading;
    }
    
    setLoading(state: boolean): void {
        this.loading = state;
    }

    // Notify that a new order has been created
    notifyOrderCreated(): void {
        this.orderCreatedSubject.next(true);
    }

    // Reset the order created notification
    resetOrderCreatedNotification(): void {
        this.orderCreatedSubject.next(false);
    }
    
    getCommonHeaders(): HttpHeaders {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        
        const token = localStorage.getItem('auth_token');
        if (token) {
            headers = headers.set('Authorization', `${token}`);
        }
        
        return headers;
    }
    
    private handleError(error: HttpErrorResponse): Observable<never> {
        let errorMessage = 'An unknown error occurred';
        
        if (error.error instanceof ErrorEvent) {
            // Client-side error
            errorMessage = `Error: ${error.error.message}`;
        } else {
            // Server-side error
            errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
            
            // Handle specific status codes
            if (error.status === 401) {
                // Unauthorized - clear token and redirect to login
                localStorage.removeItem('auth_token');
                // You could add a router injection and redirect here if needed
                errorMessage = 'Your session has expired. Please login again.';
            }
        }
        
        console.error(errorMessage);
        return throwError(() => new Error(errorMessage));
    }
    
    get<T>(endpoint: string, params?: any): Observable<T> {
        this.setLoading(true);
        const options = {
            headers: this.getCommonHeaders(),
            params: params
        };
        console.log(options);
        return this.http.get<T>(`${this.baseUrl}${endpoint}`, options).pipe(
            takeUntil(this.destroy$),
            catchError(error => this.handleError(error)),
            finalize(() => this.setLoading(false))
        );
    }
    
    post<T>(endpoint: string, body: any): Observable<T> {
        this.setLoading(true);
        const options = {
            headers: this.getCommonHeaders()
        };
        
        return this.http.post<T>(`${this.baseUrl}${endpoint}`, body, options).pipe(
            takeUntil(this.destroy$),
            catchError(error => this.handleError(error)),
            finalize(() => this.setLoading(false))
        );
    }
    
    put<T>(endpoint: string, body: any): Observable<T> {
        this.setLoading(true);
        const options = {
            headers: this.getCommonHeaders()
        };
        
        return this.http.put<T>(`${this.baseUrl}${endpoint}`, body, options).pipe(
            takeUntil(this.destroy$),
            catchError(error => this.handleError(error)),
            finalize(() => this.setLoading(false))
        );
    }
    
    delete<T>(endpoint: string): Observable<T> {
        this.setLoading(true);
        const options = {
            headers: this.getCommonHeaders()
        };
        
        return this.http.delete<T>(`${this.baseUrl}${endpoint}`, options).pipe(
            takeUntil(this.destroy$),
            catchError(error => this.handleError(error)),
            finalize(() => this.setLoading(false))
        );
    }
}