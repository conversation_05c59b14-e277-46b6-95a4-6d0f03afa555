import { Injectable } from "@angular/core";
@Injectable({
    providedIn: 'root'
})
export class DataService {
    allProducts = [
        {name: 'Carrot', price: 10, mrp: 15, image: 'https://cdn.pixabay.com/photo/2015/03/14/14/00/carrots-673184_640.jpg', category: 1, id: 1},
        {name: '<PERSON><PERSON><PERSON>', price: 20, mrp: 25, image: 'https://cdn.pixabay.com/photo/2014/08/06/20/32/potatoes-411975_640.jpg', category: 1, id: 2},
        {name: 'Onion', price: 30, mrp: 35, image: 'https://cdn.pixabay.com/photo/2016/05/16/22/47/onions-1397037_640.jpg', category: 1, id: 3},
        {name: '<PERSON><PERSON>', price: 40, mrp: 45, image: 'https://cdn.pixabay.com/photo/2011/03/16/16/01/tomatoes-5356_640.jpg', category: 1, id: 4},
        {name: 'Cabbage', price: 50, mrp: 55, image: 'https://cdn.pixabay.com/photo/2018/10/03/21/57/cabbage-3722498_640.jpg', category: 1, id: 5},
        {name: 'Spinach', price: 60, mrp: 65, image: 'https://cdn.pixabay.com/photo/2016/06/30/18/58/spinach-1489825_640.jpg', category: 1, id: 6},
        {name: 'Broccoli', price: 70, mrp: 75, image: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/broccoli-1238250_640.jpg', category: 1, id: 7},
        {name: 'Cauliflower', price: 80, mrp: 85, image: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/broccoli-1238250_640.jpg', category: 1, id: 8},
        {name: 'Garlic', price: 90, mrp: 95, image: 'https://cdn.pixabay.com/photo/2018/01/29/16/18/garlic-3116238_640.jpg', category: 1, id: 9},
        {name: 'Ginger', price: 100, mrp: 105, image: 'https://cdn.pixabay.com/photo/2015/03/14/08/10/ginger-673113_640.jpg', category: 1, id: 10},
        {name: 'Apple', price: 10, mrp: 15, image: 'https://cdn.pixabay.com/photo/2016/01/05/13/58/apple-1122537_640.jpg', category: 2, id: 11},
        {name: 'Banana', price: 20, mrp: 25, image: 'https://cdn.pixabay.com/photo/2016/01/03/17/59/bananas-1119790_640.jpg', category: 2, id: 12},
        {name: 'Orange', price: 30, mrp: 35, image: 'https://cdn.pixabay.com/photo/2017/01/20/15/06/oranges-1995056_640.jpg', category: 2, id: 13},
        {name: 'Grapes', price: 40, mrp: 45, image: 'https://cdn.pixabay.com/photo/2018/09/22/17/05/grapes-3695498_640.jpg', category: 2, id: 14},
        {name: 'Mango', price: 50, mrp: 55, image: 'https://cdn.pixabay.com/photo/2016/03/05/22/31/food-1239305_640.jpg', category: 2, id: 15},
        {name: 'Peach', price: 60, mrp: 65, image: 'https://cdn.pixabay.com/photo/2017/08/11/17/41/peach-2632182_640.jpg', category: 2, id: 16},
        {name: 'Pear', price: 70, mrp: 75, image: 'https://cdn.pixabay.com/photo/2016/07/22/09/59/fruits-1534494_640.jpg', category: 2, id: 17},
        {name: 'Pineapple', price: 80, mrp: 85, image: 'https://cdn.pixabay.com/photo/2015/02/14/18/10/pineapple-636562_640.jpg', category: 2, id: 18},
        {name: 'Watermelon', price: 90, mrp: 95, image: 'https://cdn.pixabay.com/photo/2015/06/19/16/48/watermelon-815072_640.jpg', category: 2, id: 19},
        {name: 'Strawberry', price: 100, mrp: 105, image: 'https://cdn.pixabay.com/photo/2018/04/29/11/54/strawberries-3359755_640.jpg', category: 2, id: 20},
        {name: 'Milk', price: 10, mrp: 15, image: 'https://cdn.pixabay.com/photo/2017/07/05/15/41/milk-2474993_640.jpg', category: 3, id: 21},
        {name: 'Cheese', price: 20, mrp: 25, image: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/cheese-1238395_640.jpg', category: 3, id: 22},
        {name: 'Egg', price: 30, mrp: 35, image: 'https://cdn.pixabay.com/photo/2015/09/17/17/19/egg-944495_640.jpg', category: 3, id: 23},
        {name: 'Butter', price: 40, mrp: 45, image: 'https://cdn.pixabay.com/photo/2017/01/11/19/56/butter-1972411_640.jpg', category: 3, id: 24},
        {name: 'Cheese', price: 50, mrp: 55, image: 'https://cdn.pixabay.com/photo/2020/05/01/02/38/cheese-5115479_640.jpg', category: 3, id: 25},
        {name: 'Egg', price: 60, mrp: 65, image: 'https://cdn.pixabay.com/photo/2016/07/23/15/24/egg-1536990_640.jpg', category: 3, id: 26},
        {name: 'Butter', price: 70, mrp: 75, image: 'https://cdn.pixabay.com/photo/2018/09/24/20/12/butter-3700513_640.jpg', category: 3, id: 27},
        {name: 'Cheese', price: 80, mrp: 85, image: 'https://cdn.pixabay.com/photo/2018/01/19/15/27/cheese-3092874_640.jpg', category: 3, id: 28},
        {name: 'Egg', price: 90, mrp: 95, image: 'https://cdn.pixabay.com/photo/2015/09/17/17/19/egg-944495_640.jpg', category: 3, id: 29},
        {name: 'Butter', price: 100, mrp: 105, image: 'https://cdn.pixabay.com/photo/2017/01/11/19/56/butter-1972411_640.jpg', category: 3, id: 30},
        {name: 'Bread', price: 10, mrp: 15, image: 'https://cdn.pixabay.com/photo/2019/03/24/14/23/bread-4077812_640.jpg', category: 4, id: 31},
        {name: 'Butter', price: 20, mrp: 25, image: 'https://cdn.pixabay.com/photo/2017/01/11/19/56/butter-1972411_640.jpg', category: 4, id: 32},
        {name: 'Cheese', price: 30, mrp: 35, image: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/cheese-1238395_640.jpg', category: 4, id: 33},
        {name: 'Egg', price: 40, mrp: 45, image: 'https://cdn.pixabay.com/photo/2015/09/17/17/19/egg-944495_640.jpg', category: 4, id: 34},
        {name: 'Butter', price: 50, mrp: 55, image: 'https://cdn.pixabay.com/photo/2018/09/24/20/12/butter-3700513_640.jpg', category: 4, id: 35},
        {name: 'Cheese', price: 60, mrp: 65, image: 'https://cdn.pixabay.com/photo/2018/01/19/15/27/cheese-3092874_640.jpg', category: 4, id: 36},
        {name: 'Egg', price: 70, mrp: 75, image: 'https://cdn.pixabay.com/photo/2016/07/23/15/24/egg-1536990_640.jpg', category: 4, id: 37},
        {name: 'Butter', price: 80, mrp: 85, image: 'https://cdn.pixabay.com/photo/2017/01/11/19/56/butter-1972411_640.jpg', category: 4, id: 38},
        {name: 'Cheese', price: 90, mrp: 95, image: 'https://cdn.pixabay.com/photo/2020/05/01/02/38/cheese-5115479_640.jpg', category: 4, id: 39},
        {name: 'Egg', price: 100, mrp: 105, image: 'https://cdn.pixabay.com/photo/2015/09/17/17/19/egg-944495_640.jpg', category: 4, id: 40},
        {name: 'Coffee', price: 10, mrp: 15, image: 'https://cdn.pixabay.com/photo/2016/11/29/13/04/beverage-1869716_640.jpg', category: 5, id: 41},
        {name: 'Tea', price: 20, mrp: 25, image: 'https://cdn.pixabay.com/photo/2016/11/29/13/04/beverage-1869716_640.jpg', category: 5, id: 42},
        {name: 'Milk', price: 30, mrp: 35, image: 'https://cdn.pixabay.com/photo/2017/07/05/15/41/milk-2474993_640.jpg', category: 5, id: 43},
        {name: 'Butter', price: 40, mrp: 45, image: 'https://cdn.pixabay.com/photo/2017/01/11/19/56/butter-1972411_640.jpg', category: 5, id: 44},
        {name: 'Cheese', price: 50, mrp: 55, image: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/cheese-1238395_640.jpg', category: 5, id: 45},
        {name: 'Egg', price: 60, mrp: 65, image: 'https://cdn.pixabay.com/photo/2015/09/17/17/19/egg-944495_640.jpg', category: 5, id: 46},
        {name: 'Butter', price: 70, mrp: 75, image: 'https://cdn.pixabay.com/photo/2018/09/24/20/12/butter-3700513_640.jpg', category: 5, id: 47},
        {name: 'Cheese', price: 80, mrp: 85, image: 'https://cdn.pixabay.com/photo/2018/01/19/15/27/cheese-3092874_640.jpg', category: 5, id: 48},
        {name: 'Egg', price: 90, mrp: 95, image: 'https://cdn.pixabay.com/photo/2016/07/23/15/24/egg-1536990_640.jpg', category: 5, id: 49},
        {name: 'Butter', price: 100, mrp: 105, image: 'https://cdn.pixabay.com/photo/2017/01/11/19/56/butter-1972411_640.jpg', category: 5, id: 50},
    ]
    categories = [
        {name: 'Vegetables', icon: 'fi fi-rr-carrot', id: 1, active: false},
        {name: 'Fruits', icon: 'fi fi-rr-apple-whole', id: 2, active: false},
        {name: 'Milk & Dairy', icon: 'fi fi-rr-milk-alt', id: 3, active: false},
        {name: 'Bakery', icon: 'fi fi-rr-bread', id: 4, active: false},
        {name: 'Beverages', icon: 'fi fi-rr-coffee', id: 5, active: false},
    ];
}