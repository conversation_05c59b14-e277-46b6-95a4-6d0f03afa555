import { Injectable } from "@angular/core";
import * as CryptoJ<PERSON> from 'crypto-js';

/**
 * StorageService provides encrypted local storage capabilities
 * using CryptoJS for secure data storage in the browser.
 */
@Injectable({
    providedIn: 'root'
})
export class StorageService {
    private readonly secretKey = 'atom-secure-storage-key';
    private readonly salt = CryptoJS.enc.Hex.parse('1557243662237b0b2d41d42d3921767c');
    private readonly iv = CryptoJS.enc.Hex.parse('532a0c2d62486737022935686c37630d');

    constructor() {}

    /**
     * Store data in localStorage with encryption
     * @param key The storage key
     * @param data The data to store
     */
    public setItem<T>(key: string, data: T): void {
        try {
            // Convert data to string
            const dataString = JSON.stringify(data);
            
            // Generate a key using PBKDF2
            const keyBytes = CryptoJS.PBKDF2(this.secretKey, this.salt, {
                keySize: 256 / 32,
                iterations: 1000,
                hasher: CryptoJS.algo.SHA256
            });
            
            // Encrypt the data
            const encrypted = CryptoJS.AES.encrypt(dataString, keyBytes, {
                iv: this.iv,
                padding: CryptoJS.pad.Pkcs7,
                mode: CryptoJS.mode.CBC
            });
            
            // Store the encrypted data
            localStorage.setItem(key, encrypted.toString());
        } catch (error) {
            console.error(`Error encrypting and storing data for key ${key}:`, error);
            throw error;
        }
    }

    /**
     * Retrieve and decrypt data from localStorage
     * @param key The storage key
     * @param defaultValue Default value if key doesn't exist
     * @returns The decrypted data or defaultValue if key doesn't exist
     */
    public getItem<T>(key: string, defaultValue?: T): T | undefined {
        const encryptedData = localStorage.getItem(key);
        if (!encryptedData) {
            return defaultValue;
        }
        
        try {
            // Generate the same key using PBKDF2
            const keyBytes = CryptoJS.PBKDF2(this.secretKey, this.salt, {
                keySize: 256 / 32,
                iterations: 1000,
                hasher: CryptoJS.algo.SHA256
            });
            
            // Decrypt the data
            const decrypted = CryptoJS.AES.decrypt(encryptedData, keyBytes, {
                iv: this.iv,
                padding: CryptoJS.pad.Pkcs7,
                mode: CryptoJS.mode.CBC
            });
            
            // Convert to string and parse as JSON
            const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);
            return JSON.parse(decryptedString) as T;
        } catch (error) {
            console.error(`Error decrypting data for key ${key}:`, error);
            // If decryption fails, return the default value
            return defaultValue;
        }
    }

    /**
     * Remove an item from localStorage
     * @param key The storage key to remove
     */
    public removeItem(key: string): void {
        localStorage.removeItem(key);
    }

    /**
     * Clear all items from localStorage
     */
    public clear(): void {
        localStorage.clear();
    }

    /**
     * Check if a key exists in localStorage
     * @param key The storage key to check
     * @returns True if the key exists
     */
    public hasKey(key: string): boolean {
        return localStorage.getItem(key) !== null;
    }

    /**
     * Get all keys in localStorage
     * @returns Array of all keys
     */
    public getKeys(): string[] {
        const keys: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
                keys.push(key);
            }
        }
        return keys;
    }
    
    /**
     * Encrypt a string directly using the same encryption settings
     * @param text The text to encrypt
     * @returns The encrypted string
     */
    public encryptString(text: string): string {
        try {
            const keyBytes = CryptoJS.PBKDF2(this.secretKey, this.salt, {
                keySize: 256 / 32,
                iterations: 1000,
                hasher: CryptoJS.algo.SHA256
            });
            
            const encrypted = CryptoJS.AES.encrypt(text, keyBytes, {
                iv: this.iv,
                padding: CryptoJS.pad.Pkcs7,
                mode: CryptoJS.mode.CBC
            });
            
            return encrypted.toString();
        } catch (error) {
            console.error('Error encrypting string:', error);
            throw error;
        }
    }
    
    /**
     * Decrypt a string directly using the same encryption settings
     * @param encryptedText The encrypted text to decrypt
     * @returns The decrypted string
     */
    public decryptString(encryptedText: string): string {
        try {
            const keyBytes = CryptoJS.PBKDF2(this.secretKey, this.salt, {
                keySize: 256 / 32,
                iterations: 1000,
                hasher: CryptoJS.algo.SHA256
            });
            
            const decrypted = CryptoJS.AES.decrypt(encryptedText, keyBytes, {
                iv: this.iv,
                padding: CryptoJS.pad.Pkcs7,
                mode: CryptoJS.mode.CBC
            });
            
            return decrypted.toString(CryptoJS.enc.Utf8);
        } catch (error) {
            console.error('Error decrypting string:', error);
            throw error;
        }
    }
}