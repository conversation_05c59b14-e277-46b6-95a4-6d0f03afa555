import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";
import { Client } from 'typesense';
@Injectable({
    providedIn: 'root'
})
export class TypeSenseService {
    client: Client | any;
    constructor() {
        this.initiTypesense();
    }

    initiTypesense() {
        this.client = new Client({
            nodes: [{
                host: environment.typesense.host,
                port: Number(environment.typesense.port),
                protocol: environment.typesense.protocol,
            }],
            apiKey: environment.typesense.apiKey,
            connectionTimeoutSeconds: 2,

        });
    }

    async getCategories(): Promise<any> {
        try {
            const params = {
                q: '*',
                page: 1,
                per_page: 30

            }
            const response = await this.client.collections('categories').documents().search(params)
            return response.hits?.map((hit: any) => hit.document) || [];
        } catch (error) {
            console.log(error);
        }
    }

    async getProductsByCategory(categoryName: string, page: number = 1, perPage: number = 20): Promise<any> {
        try {
            const params = {
                q: '*',
                filter_by: `categories:${categoryName}`,
                page: page,
                per_page: perPage
            }
            const response = await this.client.collections('products').documents().search(params)
            return {
                products: response.hits?.map((hit: any) => hit.document) || [],
                totalPages: Math.ceil(response.found / perPage),
                currentPage: page,
                totalProducts: response.found
            };
        } catch (error) {
            console.log(error);
            return {
                products: [],
                totalPages: 0,
                currentPage: 1,
                totalProducts: 0
            };
        }
    }
    async searchProducts(query: string, page: number = 1, perPage: number = 20): Promise<any> {
        try {
            const params = {
                q: query,
                query_by: 'name,description,sku',
                page: page,
                per_page: perPage
            }
            const response = await this.client.collections('products').documents().search(params)
            return {
                products: response.hits?.map((hit: any) => hit.document) || [],
                totalPages: Math.ceil(response.found / perPage),
                currentPage: page,
                totalProducts: response.found
            };
        } catch (error) {
            console.log(error);
            return {
                products: [],
                totalPages: 0,
                currentPage: 1,
                totalProducts: 0
            };
        }
    }

    async getAllProducts(page: number = 1, perPage: number = 20): Promise<any> {
        try {
            const params = {
                q: '*',
                page: page,
                per_page: perPage
            }
            const response = await this.client.collections('products').documents().search(params)
            return {
                products: response.hits?.map((hit: any) => hit.document) || [],
                totalPages: Math.ceil(response.found / perPage),
                currentPage: page,
                totalProducts: response.found
            };
        } catch (error) {
            console.log(error);
            return {
                products: [],
                totalPages: 0,
                currentPage: 1,
                totalProducts: 0
            };
        }
    }
    // to display the order items with sku from typesense in invoices page billing section
    async getProductBySku(sku: string): Promise<any> {
        try {
            console.log('Searching for product with SKU:', sku);

            // Try multiple search strategies to find the product
            let params = {
                q: sku,
                query_by: 'sku',
                filter_by: `sku:=${sku}`,
                per_page: 1
            }

            let response = await this.client.collections('products').documents().search(params);
            let products = response.hits?.map((hit: any) => hit.document) || [];

            console.log('First search result:', products);

            // If exact match fails, try without filter
            if (products.length === 0) {
                console.log('Exact match failed, trying broader search...');
                const broadParams = {
                    q: sku,
                    query_by: 'sku,name,description',
                    per_page: 10
                };

                response = await this.client.collections('products').documents().search(broadParams);
                products = response.hits?.map((hit: any) => hit.document) || [];

                console.log('Broader search results:', products);

                // Find exact SKU match from broader results
                const exactMatch = products.find((product: any) => product.sku === sku);
                if (exactMatch) {
                    console.log('Found exact SKU match in broader search:', exactMatch);
                    return exactMatch;
                }
            }

            const result = products.length > 0 ? products[0] : null;
            console.log('Final product result for SKU', sku, ':', result);
            return result;
        } catch (error) {
            console.log('Error fetching product by SKU:', error);
            return null;
        }
    }
};