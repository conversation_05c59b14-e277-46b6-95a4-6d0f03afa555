@use './tailwind.css';
@import "@ionic/angular/css/core.css";
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";
@import '@ionic/angular/css/palettes/dark.system.css';
@import "primeicons/primeicons.css";
@import "@flaticon/flaticon-uicons/css/all/all.css";
ion-content{
    --background: transparent !important;
}
ion-menu{
    --width: 280px !important;
}
html {
    font-size: 80%;
}
body{
margin: 0;
}
.p-accordionheader, .p-card-body{
padding: 12px !important;
border: none !important;
}
.p-datatable-gridlines{
.p-datatable-thead{
    tr{
        th{
            &:first-child{
            border-top-left-radius: 6px;
            }
            &:last-child{
            border-top-right-radius: 6px;
            }
        }
    }
}
.p-datatable-tbody{
    tr{
    &:last-child{
        td{
        &:first-child{
            border-bottom-left-radius: 6px;
        }
        &:last-child{
            border-bottom-right-radius: 6px;
        }
        }
    }
    }
}
}
.billing-table{
    .p-datatable-table{
        .p-datatable-thead{
            tr{
                th{
                    padding: 10px 5px !important;
                }
            }
        }
        .p-datatable-tbody{
            tr{
                td{
                    padding: 5px !important;
                }
            }
        }
    }
}